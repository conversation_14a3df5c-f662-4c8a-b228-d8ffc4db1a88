import Image from 'next/image';
import Link from 'next/link';
import { blogPosts } from '@/data/blogData';
import CTA from '@/components/CTA';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Fencing Blog - Expert Tips & Guides | GTA Fencing Company',
  description: 'Discover expert fencing tips, cost guides, and material comparisons from GTA Fencing Company. Learn about wood, vinyl, chain link, and other fencing options for your Toronto area property.',
  keywords: 'fencing blog, fence installation tips, fence costs, wood fencing, vinyl fencing, chain link fencing, Toronto fencing guides',
  robots: {
    index: true,
    follow: true,
  },
  alternates: {
    canonical: 'https://www.gtafencingcompany.com/blog',
  },
  openGraph: {
    title: 'Fencing Blog - Expert Tips & Guides | GTA Fencing Company',
    description: 'Discover expert fencing tips, cost guides, and material comparisons from GTA Fencing Company.',
    type: 'website',
    url: 'https://www.gtafencingcompany.com/blog',
    images: [
      {
        url: 'https://www.gtafencingcompany.com/Images/GTA-Fencing---The-Best-Fence-Contractor-in-Toronto.jpg',
        width: 1200,
        height: 630,
        alt: 'GTA Fencing Company Blog - Expert Fencing Tips and Guides',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Fencing Blog - Expert Tips & Guides | GTA Fencing Company',
    description: 'Discover expert fencing tips, cost guides, and material comparisons from GTA Fencing Company.',
    images: ['https://www.gtafencingcompany.com/Images/GTA-Fencing---The-Best-Fence-Contractor-in-Toronto.jpg'],
  },
};

export default function BlogPage() {
  // Sort blog posts by publish date (newest first)
  const sortedPosts = Object.values(blogPosts).sort((a, b) => 
    new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime()
  );

  // Get unique categories
  const categories = Array.from(new Set(sortedPosts.map(post => post.category)));

  // Generate structured data for the blog
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Blog",
    "name": "GTA Fencing Company Blog",
    "description": "Expert fencing tips, cost guides, and material comparisons from Toronto's leading fence contractor.",
    "url": "https://www.gtafencingcompany.com/blog",
    "publisher": {
      "@type": "Organization",
      "name": "GTA Fencing Company",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.gtafencingcompany.com/Images/gta-fencing-logo.png",
        "width": 150,
        "height": 50
      }
    },
    "blogPost": sortedPosts.map(post => ({
      "@type": "BlogPosting",
      "headline": post.title,
      "description": post.excerpt,
      "url": `https://www.gtafencingcompany.com/blog/${post.slug}`,
      "datePublished": post.publishDate,
      "dateModified": post.lastModified,
      "author": {
        "@type": "Organization",
        "name": post.author.name
      },
      "image": {
        "@type": "ImageObject",
        "url": `https://www.gtafencingcompany.com${post.image.url}`,
        "width": post.image.width,
        "height": post.image.height
      }
    }))
  };

  return (
    <div className="bg-white">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      {/* Hero Section */}
      <section className="bg-deep-navy text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            {/* Breadcrumb */}
            <nav className="mb-6 text-sm">
              <Link href="/" className="text-warm-off-white hover:text-premium-gold transition-colors">
                Home
              </Link>
              <span className="mx-2 text-warm-off-white">/</span>
              <span className="text-premium-gold">Blog</span>
            </nav>

            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Fencing Expert Tips & Guides
            </h1>
            <p className="text-lg md:text-xl text-warm-off-white leading-relaxed max-w-3xl mx-auto">
              Discover expert insights on fence installation, costs, materials, and maintenance from Toronto&apos;s leading fence contractor. Get the knowledge you need to make informed decisions about your fencing project.
            </p>
          </div>
        </div>
      </section>

      {/* Categories Filter */}
      <section className="py-8 bg-warm-off-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-lg font-semibold text-deep-navy mb-4">Browse by Category:</h2>
            <div className="flex flex-wrap gap-3">
              <Link
                href="/blog/"
                className="bg-premium-gold text-deep-navy px-4 py-2 rounded-full font-semibold hover:bg-deep-navy hover:text-premium-gold transition-colors duration-200"
              >
                All Posts
              </Link>
              {categories.map((category) => (
                <span
                  key={category}
                  className="bg-white text-rich-charcoal px-4 py-2 rounded-full font-medium border border-rich-charcoal hover:bg-rich-charcoal hover:text-white transition-colors duration-200 cursor-pointer"
                >
                  {category}
                </span>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {sortedPosts.map((post) => (
                <article key={post.slug} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                  {/* Featured Image */}
                  <div className="relative h-48">
                    <Image
                      src={post.image.url}
                      alt={post.image.alt}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                    <div className="absolute top-4 left-4">
                      <span className="bg-premium-gold text-deep-navy px-3 py-1 rounded-full text-sm font-semibold">
                        {post.category}
                      </span>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    {/* Meta Info */}
                    <div className="flex items-center gap-4 mb-3 text-sm text-rich-charcoal">
                      <time dateTime={post.publishDate}>
                        {new Date(post.publishDate).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </time>
                      <span>•</span>
                      <span>{post.readingTime} min read</span>
                    </div>

                    {/* Title */}
                    <h2 className="text-xl font-bold text-deep-navy mb-3 line-clamp-2">
                      <Link
                        href={`/blog/${post.slug}/`}
                        className="hover:text-premium-gold transition-colors duration-200"
                      >
                        {post.title}
                      </Link>
                    </h2>

                    {/* Excerpt */}
                    <p className="text-rich-charcoal mb-4 line-clamp-3 leading-relaxed">
                      {post.excerpt}
                    </p>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {post.tags.slice(0, 3).map((tag) => (
                        <span
                          key={tag}
                          className="bg-soft-beige text-rich-charcoal px-2 py-1 rounded text-xs font-medium"
                        >
                          {tag}
                        </span>
                      ))}
                      {post.tags.length > 3 && (
                        <span className="text-xs text-rich-charcoal">
                          +{post.tags.length - 3} more
                        </span>
                      )}
                    </div>

                    {/* Read More Link */}
                    <Link
                      href={`/blog/${post.slug}/`}
                      className="inline-flex items-center text-premium-gold hover:text-deep-navy font-semibold transition-colors duration-200"
                    >
                      Read Full Article
                      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Signup Section */}
      <section className="py-16 bg-soft-beige">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-2xl md:text-3xl font-bold text-deep-navy mb-4">
              Stay Updated with Fencing Tips
            </h2>
            <p className="text-rich-charcoal mb-8 leading-relaxed">
              Get the latest fencing insights, cost guides, and expert tips delivered to your inbox. 
              Stay informed about the best practices for your fencing projects.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <input
                type="email"
                placeholder="Enter your email address"
                className="px-4 py-3 rounded-lg border border-rich-charcoal focus:outline-none focus:ring-2 focus:ring-premium-gold flex-1 max-w-md"
              />
              <button className="bg-premium-gold text-deep-navy px-6 py-3 rounded-lg font-semibold hover:bg-deep-navy hover:text-premium-gold transition-colors duration-200">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-deep-navy text-white text-center">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-4">Ready to Start Your Fencing Project?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto">
            Put our expert knowledge to work for you. Get a free consultation and quote for your fencing needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <CTA text="Get Free Quote" link="/contact" />
            <Link
              href="/services/"
              className="bg-transparent border-2 border-warm-gold text-warm-gold px-8 py-3 rounded-lg font-semibold hover:bg-warm-gold hover:text-deep-navy transition-colors duration-200"
            >
              View Our Services
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
