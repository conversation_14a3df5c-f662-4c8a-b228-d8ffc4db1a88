import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Image optimization configuration
  images: {
    // Enable modern image formats with fallback
    formats: ['image/avif', 'image/webp'],

    // Device breakpoints for responsive images
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],

    // Image sizes for images with sizes prop (smaller than deviceSizes)
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],

    // Quality settings - restrict to specific values for consistency
    qualities: [50, 75, 90],

    // Cache TTL for optimized images (1 hour)
    minimumCacheTTL: 3600,

    // Enable static image imports
    disableStaticImages: false,

    // Local patterns for security (restrict to our images directory)
    localPatterns: [
      {
        pathname: '/Images/**',
        search: '',
      },
    ],
  },

  // URL trailing slash configuration
  trailingSlash: true,

  async redirects() {
    return [
      // 301 redirects for old blog post URLs to new blog structure
      {
        source: '/2022/09/18/how-much-does-it-cost-to-build-a-fence-around-a-house',
        destination: '/blog/how-much-does-it-cost-to-build-a-fence-around-a-house/',
        permanent: true,
      },
      {
        source: '/2022/09/18/what-is-the-cheapest-fence-to-install',
        destination: '/blog/what-is-the-cheapest-fence-to-install/',
        permanent: true,
      },
      {
        source: '/2022/09/18/is-vinyl-fence-cheaper-than-wood',
        destination: '/blog/is-vinyl-fence-cheaper-than-wood/',
        permanent: true,
      },
      // 301 redirects for old service and location URLs
      {
        source: '/pool-fence-installation-toronto',
        destination: '/services/pool-fencing/',
        permanent: true,
      },
      {
        source: '/fence-repair-toronto',
        destination: '/services/fence-repair-services/',
        permanent: true,
      },
      {
        source: '/chain-link-fence-installation-toronto',
        destination: '/services/chain-link-fencing/',
        permanent: true,
      },
      {
        source: '/wood-fence-installation-toronto',
        destination: '/locations/toronto/',
        permanent: true,
      },
      {
        source: '/vinyl-fence-installation-toronto',
        destination: '/services/vinyl-fencing/',
        permanent: true,
      },
      {
        source: '/fence-installation-in-rosedale-toronto',
        destination: '/locations/toronto/',
        permanent: true,
      },
      {
        source: '/garden-fence-installation-toronto',
        destination: '/services/backyard-fence-installation/',
        permanent: true,
      },
      // Redirect URLs without trailing slash to URLs with trailing slash
      {
        source: '/((?!api/).*[^/])$',
        destination: '/$1/',
        permanent: true,
      },
    ];
  },
};

export default nextConfig;
