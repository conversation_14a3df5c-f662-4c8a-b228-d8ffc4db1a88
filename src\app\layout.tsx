import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "GTA Fencing Company - Fencing Services - Greater Toronto Area, Ontario",
  description: "As the GTA's leading fencing specialists, GTA Fencing Company provides expert installation, repair, and maintenance for homeowners and businesses across Toronto, Mississauga, Brampton, and the surrounding areas. We specialize in a wide range of fencing solutions designed for Southern Ontario's climate.",
  robots: {
    index: true,
    follow: true,
  },
  alternates: {
    canonical: "https://www.gtafencingcompany.com",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Header />
        <main>{children}</main>
        <Footer />
      </body>
    </html>
  );
}
