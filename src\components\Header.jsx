import Link from 'next/link';
import Image from 'next/image';

const Header = () => {
  return (
    <header className="bg-deep-navy text-white shadow-md">
      <div className="container mx-auto flex items-center justify-between p-4">
        <div className="flex items-center">
          <Link href="/" passHref>
            <Image
              src="/Images/gta-fencing-logo.png"
              alt="GTA Fencing Logo"
              width={150}
              height={50}
              className="cursor-pointer"
            />
          </Link>
        </div>
        <nav className="hidden md:flex items-center space-x-6">
          <Link href="/" className="hover:text-premium-gold transition-colors">Home</Link>
          <Link href="/about/" className="hover:text-premium-gold transition-colors">About</Link>
          <Link href="/services/" className="hover:text-premium-gold transition-colors">Services</Link>
          <Link href="/locations/" className="hover:text-premium-gold transition-colors">Locations</Link>
          <Link href="/blog/" className="hover:text-premium-gold transition-colors">Blog</Link>
          <Link href="/contact/" className="hover:text-premium-gold transition-colors">Contact</Link>
        </nav>
        <div className="flex items-center space-x-4">
          <a href="tel:************" className="bg-bright-orange-red text-white px-4 py-2 rounded-md text-sm font-semibold hover:bg-opacity-90 transition-all">
            (*************
          </a>
          <button className="md:hidden text-white">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16m-7 6h7"></path>
            </svg>
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;